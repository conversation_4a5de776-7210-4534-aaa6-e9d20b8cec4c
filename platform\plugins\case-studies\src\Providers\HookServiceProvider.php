<?php

namespace <PERSON><PERSON>qi\CaseStudies\Providers;

use <PERSON><PERSON>qi\Base\Supports\ServiceProvider;
use <PERSON><PERSON>qi\CaseStudies\Services\CaseStudyService;
use <PERSON><PERSON>qi\Slug\Events\UpdatedSlugEvent;
use <PERSON><PERSON>qi\Theme\Facades\SiteMapManager;
use Illuminate\Support\Facades\Event;

class HookServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        add_filter(BASE_FILTER_PUBLIC_SINGLE_DATA, [$this, 'handleSingleView'], 1);
        add_filter(BASE_FILTER_SITE_MAP_FOR_ADMIN_LIST, [$this, 'addSiteMap'], 1);
        add_filter(BASE_FILTER_SITE_MAP_URL, [$this, 'getSiteMapUrl'], 1);

        Event::listen(UpdatedSlugEvent::class, [$this, 'handleUpdatedSlugEvent']);
    }

    public function handleSingleView($slug)
    {
        return (new CaseStudyService())->handleFrontRoutes($slug);
    }

    public function addSiteMap($data): array
    {
        $data[] = [
            'key' => 'case-studies',
            'name' => __('Case Studies'),
        ];

        $data[] = [
            'key' => 'case-study-categories',
            'name' => __('Case Study Categories'),
        ];

        return $data;
    }

    public function getSiteMapUrl($key): string
    {
        switch ($key) {
            case 'case-studies':
            case 'case-study-categories':
                return (new CaseStudyService())->getSiteMap();
        }

        return '';
    }

    public function handleUpdatedSlugEvent(UpdatedSlugEvent $event): void
    {
        if (get_class($event->data) == CaseStudy::class || get_class($event->data) == CaseStudyCategory::class) {
            SiteMapManager::update();
        }
    }
}
