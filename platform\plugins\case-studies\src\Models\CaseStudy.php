<?php

namespace Shaqi\CaseStudies\Models;

use <PERSON>haqi\ACL\Models\User;
use <PERSON>haqi\Base\Casts\SafeContent;
use <PERSON>haqi\Base\Enums\BaseStatusEnum;
use <PERSON>haqi\Base\Models\BaseModel;
use <PERSON>haqi\Revision\RevisionableTrait;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class CaseStudy extends BaseModel
{
    use RevisionableTrait;

    protected $table = 'case_studies';

    protected bool $revisionEnabled = true;

    protected bool $revisionCleanup = true;

    protected int $historyLimit = 20;

    protected array $dontKeepRevisionOf = [
        'content',
        'views',
    ];

    protected $fillable = [
        'name',
        'description',
        'content',
        'image',
        'client_name',
        'client_logo',
        'project_url',
        'challenge',
        'solution',
        'results',
        'technologies',
        'gallery',
        'is_featured',
        'status',
        'author_id',
        'author_type',
    ];

    protected static function booted(): void
    {
        static::deleted(function (self $caseStudy): void {
            $caseStudy->categories()->detach();
        });

        static::creating(function (self $caseStudy): void {
            $caseStudy->author_id = $caseStudy->author_id ?: auth()->id();
            $caseStudy->author_type = $caseStudy->author_type ?: User::class;
        });
    }

    protected $casts = [
        'status' => BaseStatusEnum::class,
        'name' => SafeContent::class,
        'description' => SafeContent::class,
        'content' => SafeContent::class,
        'challenge' => SafeContent::class,
        'solution' => SafeContent::class,
        'results' => SafeContent::class,
        'technologies' => 'array',
        'gallery' => 'array',
        'is_featured' => 'boolean',
    ];

    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(CaseStudyCategory::class, 'case_study_category_pivot', 'case_study_id', 'category_id');
    }
}
